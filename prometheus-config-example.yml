# Prometheus 相关配置示例
# 解决 async_task_stat 数据缺失问题

management:
  endpoints:
    web:
      exposure:
        include: "*"
      # 增加响应超时时间
      base-path: /actuator
  endpoint:
    prometheus:
      enabled: true
      # 缓存时间，避免频繁计算
      cache:
        time-to-live: 10s
  metrics:
    export:
      prometheus:
        enabled: true
        # 启用描述信息
        descriptions: true
        # 设置步长
        step: 1m
    # 启用所有指标
    enable:
      all: true

# 自定义配置
asyncTaskStat:
  monitor:
    # response 字段长度限制
    res:
      length: 20
    # 标签值最大长度限制（避免 Prometheus 标签值过长）
    tag:
      maxLength: 50

# 服务器配置
server:
  # 增加最大响应大小
  max-http-header-size: 8KB
  # 连接超时
  connection-timeout: 20000

# 日志配置
logging:
  level:
    # 开启 Prometheus 相关日志
    io.micrometer.prometheus: DEBUG
    # 开启异步任务统计日志
    com.iqiyi.vip.job.AsyncTaskStatScheduler: INFO

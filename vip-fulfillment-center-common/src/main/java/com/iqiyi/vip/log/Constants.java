package com.iqiyi.vip.log;

import com.google.common.collect.ImmutableMap;

import com.iqiyi.vip.enums.GiftCodeEnum;
import com.iqiyi.vip.enums.SpuCategoryEnum;

/**
 * 用于处理HTTP请求的工具类
 *
 * <AUTHOR>
 * @date 16-8-2
 */
public interface Constants {

    /**
     * httpLog中response返回值截取的长度
     */
    int APP_LOG_RESPONSE_LENGTH = 5000;
    /**
     * TV端域名后缀
     **/
    String TV_HOSTS = ".vip.ptqy.gitv.tv";

    /**
     * header 常量定义
     */
    String HEADER_USER_AGENT = "User-Agent";
    String HEADER_REFERER = "Referer";
    String HEADER_X_FORWARDED_FOR = "X-Forwarded-For";
    String HEADER_TICKET = "P00001";

    String COOKIE_QC005 = "QC005";
    String COOKIE_AUTH_COOKIE_KEY = "P00001";
    String COOKIE_QIYUE_CK_KEY = "QIYUECK";

    // 买赠系统赠送的订单标识
    String PRESENT_ACT_CODE = "vip_present";

    //todo 打包购子商品skuAmount默认为1
    int PACK_SUB_SKU_DEFAULT_SKU_AMOUNT = 1;

    /**
     * 调用交易系统的验签参数
     */
    String SIGNATURE_HEADER = "X-Signature";
    String SOURCE_HEADER = "X-Source";

    /**
     * 会员商品类目与gitCode的映射map
     */
    ImmutableMap<String, String> vip_gitCode_map = ImmutableMap.of(
        SpuCategoryEnum.VIP.getCategoryId(), GiftCodeEnum.VIP_GIFT_CODE.getGiftCode(),
        SpuCategoryEnum.VOD.getCategoryId(), GiftCodeEnum.VOD_GIFT_CODE.getGiftCode(),
        SpuCategoryEnum.PACKAGE.getCategoryId(), GiftCodeEnum.PACKAGE_GIFT_CODE.getGiftCode()
    );

}
